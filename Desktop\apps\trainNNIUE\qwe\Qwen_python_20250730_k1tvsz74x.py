import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import os
import json
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import chess
import chess.pgn
import random
import datetime
from tqdm import tqdm

# ==================== IMPORT TRAINING LOGIC ====================
# Assume the training logic from the previous code is in a separate module
from chessnet_training import ChessNet, parse_pgn_files, ChessDataset, train_epoch, validate

# ==================== GLOBAL VARIABLES ====================
training_thread = None
model = ChessNet()
train_loader = None
val_loader = None
train_losses = []
val_losses = []
is_training = False

# ==================== GUI CLASS ====================
class ChessTrainerGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("ChessNet Trainer")
        self.geometry("800x600")
        
        # Tabs
        self.notebook = ttk.Notebook(self)
        self.training_tab = tk.Frame(self.notebook)
        self.config_tab = tk.Frame(self.notebook)
        self.analysis_tab = tk.Frame(self.notebook)
        self.notebook.add(self.training_tab, text="Training")
        self.notebook.add(self.config_tab, text="Configuration")
        self.notebook.add(self.analysis_tab, text="Analysis")
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Initialize tabs
        self.create_training_tab()
        self.create_config_tab()
        self.create_analysis_tab()
        
        # Load configuration if exists
        self.load_config()

    # ==================== TRAINING TAB ====================
    def create_training_tab(self):
        # File Selection
        file_frame = tk.LabelFrame(self.training_tab, text="File Selection")
        file_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Label(file_frame, text="Training PGN Folder:").grid(row=0, column=0, sticky=tk.W)
        self.train_pgn_entry = tk.Entry(file_frame, width=50)
        self.train_pgn_entry.grid(row=0, column=1, sticky=tk.W)
        tk.Button(file_frame, text="Browse", command=lambda: self.browse_folder(self.train_pgn_entry)).grid(row=0, column=2)
        
        tk.Label(file_frame, text="Validation PGN Folder:").grid(row=1, column=0, sticky=tk.W)
        self.val_pgn_entry = tk.Entry(file_frame, width=50)
        self.val_pgn_entry.grid(row=1, column=1, sticky=tk.W)
        tk.Button(file_frame, text="Browse", command=lambda: self.browse_folder(self.val_pgn_entry)).grid(row=1, column=2)
        
        # Training Controls
        control_frame = tk.LabelFrame(self.training_tab, text="Controls")
        control_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Button(control_frame, text="Start Training", command=self.start_training).grid(row=0, column=0, padx=5)
        tk.Button(control_frame, text="Stop Training", command=self.stop_training, state=tk.DISABLED).grid(row=0, column=1, padx=5)
        tk.Button(control_frame, text="Run Validation", command=self.run_validation).grid(row=0, column=2, padx=5)
        tk.Button(control_frame, text="Load Latest Checkpoint", command=self.load_checkpoint).grid(row=0, column=3, padx=5)
        tk.Button(control_frame, text="Reset File Progress", command=self.reset_progress).grid(row=0, column=4, padx=5)
        
        # Progress Bar
        progress_frame = tk.LabelFrame(self.training_tab, text="Progress")
        progress_frame.pack(pady=10, padx=10, fill=tk.X)
        self.progress_label = tk.Label(progress_frame, text="Ready")
        self.progress_label.pack(padx=10, pady=10)
        
        # Loss Plot
        plot_frame = tk.LabelFrame(self.training_tab, text="Training Progress")
        plot_frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        self.fig = Figure(figsize=(6, 4), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epochs")
        self.ax.set_ylabel("Loss")
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # ==================== CONFIGURATION TAB ====================
    def create_config_tab(self):
        config_frame = tk.LabelFrame(self.config_tab, text="Training Parameters")
        config_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Label(config_frame, text="Epochs:").grid(row=0, column=0, sticky=tk.W)
        self.epochs_entry = tk.Entry(config_frame, width=10)
        self.epochs_entry.grid(row=0, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Batch Size:").grid(row=1, column=0, sticky=tk.W)
        self.batch_size_entry = tk.Entry(config_frame, width=10)
        self.batch_size_entry.grid(row=1, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Learning Rate:").grid(row=2, column=0, sticky=tk.W)
        self.lr_entry = tk.Entry(config_frame, width=10)
        self.lr_entry.grid(row=2, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Weight Decay:").grid(row=3, column=0, sticky=tk.W)
        self.weight_decay_entry = tk.Entry(config_frame, width=10)
        self.weight_decay_entry.grid(row=3, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Validation Interval:").grid(row=4, column=0, sticky=tk.W)
        self.val_interval_entry = tk.Entry(config_frame, width=10)
        self.val_interval_entry.grid(row=4, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Checkpoint Interval:").grid(row=5, column=0, sticky=tk.W)
        self.checkpoint_interval_entry = tk.Entry(config_frame, width=10)
        self.checkpoint_interval_entry.grid(row=5, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Early Stopping Patience:").grid(row=6, column=0, sticky=tk.W)
        self.patience_entry = tk.Entry(config_frame, width=10)
        self.patience_entry.grid(row=6, column=1, sticky=tk.W)
        
        tk.Checkbutton(config_frame, text="Auto-save Best Model", variable=tk.BooleanVar()).grid(row=7, column=0, sticky=tk.W)
        tk.Checkbutton(config_frame, text="Auto-load Latest Checkpoint on Start", variable=tk.BooleanVar()).grid(row=8, column=0, sticky=tk.W)
        tk.Checkbutton(config_frame, text="Enable Time-Based Checkpoints", variable=tk.BooleanVar()).grid(row=9, column=0, sticky=tk.W)
        
        tk.Button(config_frame, text="Apply Configuration", command=self.apply_config).grid(row=10, column=0, columnspan=2, pady=10)

    # ==================== ANALYSIS TAB ====================
    def create_analysis_tab(self):
        analysis_frame = tk.LabelFrame(self.analysis_tab, text="Position Evaluation")
        analysis_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Label(analysis_frame, text="FEN:").grid(row=0, column=0, sticky=tk.W)
        self.fen_entry = tk.Entry(analysis_frame, width=50)
        self.fen_entry.grid(row=0, column=1, sticky=tk.W)
        tk.Button(analysis_frame, text="Evaluate", command=self.evaluate_position).grid(row=0, column=2, padx=5)
        
        self.evaluation_label = tk.Label(analysis_frame, text="Evaluation: Not calculated")
        self.evaluation_label.grid(row=1, column=0, columnspan=3, pady=10)

    # ==================== HELPER FUNCTIONS ====================
    def browse_folder(self, entry):
        folder = filedialog.askdirectory()
        if folder:
            entry.delete(0, tk.END)
            entry.insert(0, folder)

    def load_config(self):
        try:
            with open("config.json", "r") as f:
                config = json.load(f)
                self.train_pgn_entry.delete(0, tk.END)
                self.train_pgn_entry.insert(0, config["train_pgn_dir"])
                self.val_pgn_entry.delete(0, tk.END)
                self.val_pgn_entry.insert(0, config["val_pgn_dir"])
                self.epochs_entry.delete(0, tk.END)
                self.epochs_entry.insert(0, str(config["epochs"]))
                self.batch_size_entry.delete(0, tk.END)
                self.batch_size_entry.insert(0, str(config["batch_size"]))
                self.lr_entry.delete(0, tk.END)
                self.lr_entry.insert(0, str(config["learning_rate"]))
                self.weight_decay_entry.delete(0, tk.END)
                self.weight_decay_entry.insert(0, str(config["weight_decay"]))
                self.val_interval_entry.delete(0, tk.END)
                self.val_interval_entry.insert(0, str(config["validation_interval"]))
                self.checkpoint_interval_entry.delete(0, tk.END)
                self.checkpoint_interval_entry.insert(0, str(config["checkpoint_interval"]))
                self.patience_entry.delete(0, tk.END)
                self.patience_entry.insert(0, str(config["early_stopping_patience"]))
        except FileNotFoundError:
            pass

    def save_config(self):
        config = {
            "train_pgn_dir": self.train_pgn_entry.get(),
            "val_pgn_dir": self.val_pgn_entry.get(),
            "epochs": int(self.epochs_entry.get()),
            "batch_size": int(self.batch_size_entry.get()),
            "learning_rate": float(self.lr_entry.get()),
            "weight_decay": float(self.weight_decay_entry.get()),
            "validation_interval": int(self.val_interval_entry.get()),
            "checkpoint_interval": int(self.checkpoint_interval_entry.get()),
            "early_stopping_patience": int(self.patience_entry.get()),
        }
        with open("config.json", "w") as f:
            json.dump(config, f, indent=2)

    def apply_config(self):
        self.save_config()
        messagebox.showinfo("Configuration", "Configuration applied successfully.")

    def start_training(self):
        global training_thread, is_training
        if not is_training:
            is_training = True
            self.update_controls(is_training=True)
            
            # Get configuration
            train_pgn_dir = self.train_pgn_entry.get()
            val_pgn_dir = self.val_pgn_entry.get()
            epochs = int(self.epochs_entry.get())
            batch_size = int(self.batch_size_entry.get())
            lr = float(self.lr_entry.get())
            weight_decay = float(self.weight_decay_entry.get())
            val_interval = int(self.val_interval_entry.get())
            
            # Prepare data
            train_positions = parse_pgn_files(train_pgn_dir)
            val_positions = parse_pgn_files(val_pgn_dir)
            train_dataset = ChessDataset(train_positions)
            val_dataset = ChessDataset(val_positions)
            global train_loader, val_loader
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # Start training thread
            training_thread = threading.Thread(target=self.run_training, args=(epochs, lr, weight_decay, val_interval))
            training_thread.start()

    def stop_training(self):
        global is_training
        is_training = False
        self.update_controls(is_training=False)

    def run_training(self, epochs, lr, weight_decay, val_interval):
        global model, train_loader, val_loader, train_losses, val_losses
        
        # Initialize model and optimizer
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        
        for epoch in range(epochs):
            if not is_training:
                break
            
            # Train epoch
            train_loss = train_epoch(model, train_loader, optimizer, device)
            train_losses.append(train_loss)
            
            # Validate
            if (epoch + 1) % val_interval == 0:
                val_loss = validate(model, val_loader, device)
                val_losses.append(val_loss)
                
                # Update plot
                self.update_plot()
            
            # Update progress
            self.progress_label.config(text=f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}")
        
        self.progress_label.config(text="Training Complete!")
        self.update_controls(is_training=False)

    def update_plot(self):
        self.ax.clear()
        self.ax.plot(range(len(train_losses)), train_losses, label="Train Loss")
        self.ax.plot(range(len(val_losses)), val_losses, label="Validation Loss")
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epochs")
        self.ax.set_ylabel("Loss")
        self.ax.legend()
        self.canvas.draw()

    def update_controls(self, is_training):
        self.training_tab.children["!button"].config(state=tk.NORMAL if not is_training else tk.DISABLED)
        self.training_tab.children["!button2"].config(state=tk.NORMAL if is_training else tk.DISABLED)
        self.training_tab.children["!button3"].config(state=tk.NORMAL if not is_training else tk.DISABLED)
        self.training_tab.children["!button4"].config(state=tk.NORMAL if not is_training else tk.DISABLED)
        self.training_tab.children["!button5"].config(state=tk.NORMAL if not is_training else tk.DISABLED)

    def run_validation(self):
        global model, val_loader
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        val_loss = validate(model, val_loader, device)
        messagebox.showinfo("Validation", f"Validation Loss: {val_loss:.4f}")

    def load_checkpoint(self):
        global model
        checkpoint_path = filedialog.askopenfilename(defaultextension=".pth")
        if checkpoint_path:
            model.load_state_dict(torch.load(checkpoint_path))
            messagebox.showinfo("Checkpoint", "Latest checkpoint loaded successfully.")

    def reset_progress(self):
        global train_losses, val_losses
        train_losses = []
        val_losses = []
        self.update_plot()
        self.progress_label.config(text="Ready")

    def evaluate_position(self):
        fen = self.fen_entry.get()
        try:
            board = chess.Board(fen)
            # Create a temporary dataset instance to use the fen_to_tensor method
            temp_dataset = ChessDataset([])
            board_tensor = temp_dataset.fen_to_tensor(board.fen())
            board_tensor = torch.unsqueeze(board_tensor, 0)  # Add batch dimension
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model.eval()
            with torch.no_grad():
                policy_pred, value_pred = model(board_tensor.to(device))
            
            value = value_pred.item()
            self.evaluation_label.config(text=f"Evaluation: {value:.4f}")
        except Exception as e:
            messagebox.showerror("Error", f"Invalid FEN: {str(e)}")

# ==================== MAIN ====================
if __name__ == "__main__":
    app = ChessTrainerGUI()
    app.mainloop()